import os
import json
import google.generativeai as genai # <-- (1) إضافة المكتبة الجديدة
from dotenv import load_dotenv
from functools import wraps
from telegram import Update, ReplyKeyboardMarkup, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.ext import (
    Application,
    CommandHandler,
    MessageHandler,
    filters,
    ContextTypes,
    ConversationHandler,
    CallbackQueryHandler,
)
from telegram.error import BadRequest

# 1. تحميل متغيرات البيئة من ملف .env
load_dotenv('.env')

# --- الإعدادات والمتغيرات الأساسية (يتم تحميلها من ملف .env) ---
TELEGRAM_TOKEN = os.getenv('TELEGRAM_TOKEN')
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY') # <-- قراءة مفتاح جوجل
CHANNEL_ID = os.getenv('CHANNEL_ID')
GROUP_ID_STR = os.getenv('GROUP_ID')
GROUP_ID = int(GROUP_ID_STR) if GROUP_ID_STR else None
CHANNEL_LINK = os.getenv('CHANNEL_LINK')
GROUP_LINK = os.getenv('GROUP_LINK')

# التحقق من أن كل المتغيرات المطلوبة موجودة
if not all([TELEGRAM_TOKEN, GOOGLE_API_KEY, CHANNEL_ID, GROUP_ID, CHANNEL_LINK, GROUP_LINK]):
    raise ValueError("أحد المتغيرات المطلوبة غير موجود في ملف .env! يرجى مراجعة الملف.")

# --- (2) إعداد Google AI ---
genai.configure(api_key=GOOGLE_API_KEY)

# 2. تحميل البيانات من ملف JSON
try:
    with open('data.json', 'r', encoding='utf-8') as f:
        MAJORS_DATA = json.load(f)
except (FileNotFoundError, json.JSONDecodeError) as e:
    print(f"خطأ في تحميل ملف data.json: {e}")
    MAJORS_DATA = {}

(AI_CONVERSION,) = range(1)

# --- (3) دالة جديدة للتواصل مع Gemini ---
async def get_gemini_response(prompt: str) -> str:
    try:
        model = genai.GenerativeModel('gemini-pro')
        response = await model.generate_content_async(prompt)
        return response.text
    except Exception as e:
        print(f"Error calling Google AI: {e}")
        return "عذراً، حدث خطأ أثناء التواصل مع الذكاء الاصطناعي. قد تكون هناك مشكلة في الخدمة حالياً."


# --- بقية الدوال تبقى كما هي ---
# (check_membership, start, show_majors, show_services, etc...)
# ... (لا تغيير هنا) ...
def check_membership(func):
    @wraps(func)
    async def wrapped(update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs):
        user_id = update.effective_user.id
        try:
            # التحقق من القناة
            if (await context.bot.get_chat_member(chat_id=CHANNEL_ID, user_id=user_id)).status in ['left', 'kicked']:
                keyboard = [[InlineKeyboardButton("اضغط هنا للاشتراك في القناة 📢", url=CHANNEL_LINK)]]
                await update.message.reply_text("عذراً، يجب عليك الاشتراك في القناة أولاً لاستخدام البوت.", reply_markup=InlineKeyboardMarkup(keyboard))
                return
            # التحقق من المجموعة
            if (await context.bot.get_chat_member(chat_id=GROUP_ID, user_id=user_id)).status in ['left', 'kicked']:
                keyboard = [[InlineKeyboardButton("اضغط هنا للانضمام للمجموعة 💬", url=GROUP_LINK)]]
                await update.message.reply_text("خطوة أخيرة! يجب عليك الانضمام للمجموعة لاستخدام البوت.", reply_markup=InlineKeyboardMarkup(keyboard))
                return
        except BadRequest:
            await update.message.reply_text("خطأ بالتحقق. تأكد أن البوت مشرف في القناة والمجموعة.")
            return
        return await func(update, context, *args, **kwargs)
    return wrapped

# --- دوال القائمة الرئيسية والأزرار ---
@check_membership
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    kb = []
    for code, info in MAJORS_DATA.items():
        kb.append([f"{info['name']} 📖"])
    kb.extend([['خدمات الطلاب 🛠️'], ['الدردشة مع الذكاء الاصطناعي 🤖']])
    await update.message.reply_text('أهلاً بك في بوت الديوان الجامعي!', reply_markup=ReplyKeyboardMarkup(kb, resize_keyboard=True))

@check_membership
async def show_majors(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    kb = [[InlineKeyboardButton(info['name'], callback_data=f"major_{code}")] for code, info in MAJORS_DATA.items()]
    await update.message.reply_text('الرجاء اختيار التخصص:', reply_markup=InlineKeyboardMarkup(kb))

@check_membership
async def show_services(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    kb = [[InlineKeyboardButton("حساب المعدل", callback_data='service_gpa')], [InlineKeyboardButton("التقويم الجامعي", callback_data='service_calendar')]]
    await update.message.reply_text('الخدمات المتاحة:', reply_markup=InlineKeyboardMarkup(kb))

@check_membership
async def handle_major_selection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    text = update.message.text
    selected_major = None
    selected_code = None
    for code, info in MAJORS_DATA.items():
        if f"{info['name']} 📖" == text:
            selected_major = info
            selected_code = code
            break
    if selected_major:
        kb = [[InlineKeyboardButton(c['name'], callback_data=f"course_{selected_code}_{course_code}")]
              for course_code, c in selected_major['courses'].items()]
        kb.append([InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data='back_to_main')])
        await update.message.reply_text(f"مقررات تخصص '{selected_major['name']}':", reply_markup=InlineKeyboardMarkup(kb))

async def button_callback_handler(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    query = update.callback_query
    await query.answer()
    data = query.data
    
    if data.startswith('major_'):
        major_code = data.split('_')[1]
        major = MAJORS_DATA[major_code]
        kb = [[InlineKeyboardButton(c['name'], callback_data=f"course_{major_code}_{code}")] for code, c in major['courses'].items()]
        kb.append([InlineKeyboardButton("🔙 العودة للتخصصات", callback_data='back_to_majors')])
        await query.edit_message_text(text=f"اختر أحد مقررات '{major['name']}':", reply_markup=InlineKeyboardMarkup(kb))
    elif data.startswith('course_'):
        _, major_code, course_code = data.split('_')
        course = MAJORS_DATA[major_code]['courses'][course_code]
        text = f"*{course['name']}*\n\n{course['content']}"
        kb = [[InlineKeyboardButton("🔗 رابط المقرر", url=course['link'])], [InlineKeyboardButton(f"🔙 العودة لمقررات '{MAJORS_DATA[major_code]['name']}'", callback_data=f"major_{major_code}")]]
        await query.edit_message_text(text=text, reply_markup=InlineKeyboardMarkup(kb), parse_mode='Markdown')
    elif data == 'back_to_majors':
        await query.delete_message()
        await show_majors(query.message, context)
    elif data == 'back_to_main':
        await query.delete_message()
        await start(query.message, context)
    elif data.startswith('service_'):
        await query.edit_message_text(text="هذه الخدمة قيد التطوير حالياً.")

# --- دوال الذكاء الاصطناعي ---
@check_membership
async def start_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await update.message.reply_text('أنت الآن في وضع الدردشة الذكية. للعودة اضغط الزر أدناه.', reply_markup=ReplyKeyboardMarkup([['العودة للقائمة الرئيسية 🔙']], resize_keyboard=True))
    return AI_CONVERSION

# --- (4) تعديل هذه الدالة لتستخدم Gemini ---
async def handle_ai_conversation(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    msg = await update.message.reply_text('...🤔')
    
    # استدعاء دالة Gemini الجديدة
    ai_response = await get_gemini_response(update.message.text)
    
    # تعديل الرسالة بالرد من الذكاء الاصطناعي
    await msg.edit_text(ai_response)

    return AI_CONVERSION

async def end_ai_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await start(update, context)
    return ConversationHandler.END

# --- الدالة الرئيسية (تبقى كما هي) ---
def main() -> None:
    app = Application.builder().token(TELEGRAM_TOKEN).build()
    ai_handler = ConversationHandler(
        entry_points=[MessageHandler(filters.Regex('^الدردشة مع الذكاء الاصطناعي 🤖$'), start_ai_chat)],
        states={AI_CONVERSION: [MessageHandler(filters.TEXT & ~filters.Regex('^العودة للقائمة الرئيسية 🔙$'), handle_ai_conversation)]},
        fallbacks=[MessageHandler(filters.Regex('^العودة للقائمة الرئيسية 🔙$'), end_ai_chat)],
    )
    app.add_handler(CommandHandler("start", start))
    app.add_handler(MessageHandler(filters.Regex('^التخصصات الجامعية 📚$'), show_majors))
    app.add_handler(MessageHandler(filters.Regex('^خدمات الطلاب 🛠️$'), show_services))
    for code, info in MAJORS_DATA.items():
        pattern = f"^{info['name']} 📖$"
        app.add_handler(MessageHandler(filters.Regex(pattern), handle_major_selection))
    app.add_handler(ai_handler)
    app.add_handler(CallbackQueryHandler(button_callback_handler))
    print("🤖 بوت الديوان الجامعي يعمل الآن...")
    print("📱 يمكنك الآن التفاعل مع البوت على تليجرام!")
    app.run_polling()

if __name__ == '__main__':
    main()